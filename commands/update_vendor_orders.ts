import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import ZnVendorOrder from "#models/zn_vendor_order";
import {PackageTrackingService} from "../app/services/shippo/package_tracking_service.js";
import queue from "@rlanz/bull-queue/services/main";
import UpdatePackageTrackingHistoryJob from "#jobs/update_package_tracking_history_job";

export default class UpdateVendorOrders extends BaseCommand {
  static commandName = 'update:vendor-orders'
  static description = ''

  static options: CommandOptions = {
    startApp: true
  }

  async run() {
    const packageTrackingService = new PackageTrackingService()
    const orders = await ZnVendorOrder.query()
      .preload('fulfillment')
      .whereNotNull('fulfillmentId');

    for (const order of orders) {
      try {
        const trackingNumber = order.fulfillment?.trackingNumber
        if (!trackingNumber) continue

        const trackingData = await packageTrackingService.getTrackings(order.fulfillment.trackingCompany, trackingNumber)
        if (!trackingData) continue
        console.log(trackingData)

        // Update history and fields
        await queue.dispatch(UpdatePackageTrackingHistoryJob, { tracking: trackingData, from: 'api' }, { queueName: 'tracking' })

      } catch (error) {
        console.error(`Failed to update tracking for order ID ${order.id}:`, error)
      }
    }
  }
}
