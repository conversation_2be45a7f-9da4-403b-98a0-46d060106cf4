import ZnProductVariant from '#models/zn_product_variant'
import { BigQueryService } from '#services/google/big_query_service'
import env from '#start/env'
import ExcelJS from 'exceljs'
import { DateTime } from 'luxon'

type InventorySurplusReportParams = {
  cutoffDate?: string
  sku?: string,
  latestOnly?: boolean,
}

export class InventorySurplusReportService {
  private bigQueryService: BigQueryService

  constructor() {
    this.bigQueryService = new BigQueryService()
  }

  async getPaginatedInventoryReport(
    params: InventorySurplusReportParams,
    paginations: {
      page: number
      limit: number
    }
  ) {
    const report = await this.fetchInventoryReport(params, paginations)

    const totalRows = report[0]?.totalRows || 0

    const result = {
      meta: {
        total: totalRows,
        lastPage: Math.ceil(totalRows / paginations.limit),
      },
      data: report,
    }

    return result
  }

  async getAllInventoryReport(params: InventorySurplusReportParams) {
    const report = await this.fetchInventoryReport(params)

    return report
  }

  async createInventoryReportWorkbook(report: any[]) {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Inventory Report')

    worksheet.addRow([
      'SKU',
      'Name',
      'Supplier',
      'Quantity Available',
      'Quantity On Hand',
      'Measurement Unit',
      "Cost Price",
      "Unit Price",
      "Retail Price",
      "Compare At Price",
      'Lastest Import Date',
      'Warehouse',
      'Location',
    ])

    const skus = report.map(rep => rep.product_code)
    const variants = await ZnProductVariant.query()
      .whereIn('sku', skus)

    for (const record of report) {
      const variant = variants.find(vari => vari.sku == record.product_code)

      worksheet.addRow([
        record.product_code,
        record.product_variant_name,
        record.supplier_name,
        record.quantity_available,
        record.quantity_on_hand,
        record.default_uom_name,
        record.cost_price.toNumber(),
        record.unit_price.toNumber(),
        variant?.price,
        variant?.compareAtPrice,
        record.effective_date.value,
        record.warehouse_name,
        record.location_name,
      ])
    }

    return workbook
  }

  private async fetchInventoryReport(
    params: InventorySurplusReportParams,
    paginations?: {
      page: number
      limit: number
    }
  ) {
    try {
      const cutoff_date = params.cutoffDate
        ? DateTime.fromISO(params.cutoffDate).toSQLDate()
        : DateTime.now().minus({ year: 1 }).toSQLDate()

      const skuQuery = params.sku
        ? `AND product_code = '${params.sku}'`
        : ''

      const paginationQuery = paginations
        ? `LIMIT ${paginations.limit} OFFSET ${(paginations.page - 1) * paginations.limit}`
        : ''

      const query = `
        WITH cte AS (
          SELECT 
            *,
            ROW_NUMBER() OVER(PARTITION BY product_code ORDER BY effective_date DESC) AS rowNumber
          FROM \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.inventory_moves\`
          WHERE from_location_type IN ('supplier')
            AND state = 'done'
            ${skuQuery}
        )
        SELECT *,
          COUNT(*) OVER() AS totalRows
        FROM cte
        JOIN \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.inventory_by_location\` il ON cte.product_id = il.product_id
        LEFT JOIN \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.product_suppliers\` ps ON cte.product_id = ps.product_variant_id
        WHERE rowNumber = 1
          AND effective_date <= '${cutoff_date}'
          AND quantity_available > 0
          AND quantity_on_hand > 0
        ORDER BY il.product_template_name, il.product_code, il.warehouse_name, il.location_name
        ${paginationQuery};
      `

      const data = await this.bigQueryService.runQuery(query)

      return data
    } catch (error) {
      console.log(error)
      return []
    }
  }

}
