import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import ZnAffiliateTier from '#models/zn_affiliate_tier'
import logger from '@adonisjs/core/services/logger'
import ZnAffiliateTierCommissionGroup from '#models/zn_affiliate_tier_commission_group'
import { AffiliationCommissionGroupService } from '#services/affiliation/affiliation_commission_group_service'
import { createCommissionGroupValidator, updateCommissionGroupValidator } from '../../validators/affiliation/affiliation_commission_group_validator.js'

export default class AdminAffiliationTierCommissionGroupsController {
  private commissionGroupService: AffiliationCommissionGroupService;

  constructor() {
    this.commissionGroupService = new AffiliationCommissionGroupService();
  }

  async showCommissionGroups({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const tierId = params.id;
      return await this.commissionGroupService.findCommissionGroupsByTierId(tierId);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async showProductsOfCommissionGroups({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const groupId = params.id;
      if (!groupId) {
        throw new Error('Group ID is required');
      }
      const { searchTerm = '', page = 1, limit = 10 } = request.all();

      return this.commissionGroupService.showProductsOfCommissionGroups(groupId, searchTerm, page, limit);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async updateProductsOfCommissionGroups({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const groupId = params.id;
      if (!groupId) {
        throw new Error('Group ID is required');
      }
      const { action, productIds, collectionId } = request.all();

      return await this.commissionGroupService.updateProductsToCommissionGroupByProductIds(action, groupId, productIds, collectionId);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async showNonCommissionProducts({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const tierId = params.id;
      if (!tierId) {
        throw new Error('Tier ID is required');
      }
      const { collectionId, searchTerm, page = 1, limit = 10 } = request.all();

      return await this.commissionGroupService.showNonCommissionProducts(tierId, collectionId, searchTerm, page, limit);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async storeCommissionGroup({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const tierId = params.id;
      if (!tierId) {
        throw new Error('Tier ID or Commission Rate is required');
      }
      const payload = await request.validateUsing(createCommissionGroupValidator);

      const tier = await ZnAffiliateTier.findOrFail(tierId);
      return await this.commissionGroupService.createCommissionGroup(tier, payload.type);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async updateCommissionGroup({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const groupId = params.id
      const payload = await request.validateUsing(updateCommissionGroupValidator);

      const group = await this.commissionGroupService.updateCommissionGroup(groupId, payload.type);
      return response.ok(group);

    } catch (error) {
      logger.error(error);
      return response.badRequest(error);
    }
  }

  async destroyCommissionGroup({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const groupId = params.id
      if (!groupId) {
        throw new Error('Group ID is required');
      }

      const group = await ZnAffiliateTierCommissionGroup.findOrFail(groupId);
      await group.softDelete();

      return response.ok(group);

    } catch (error) {
      logger.error(error);
      return response.badRequest(error);
    }
  }
}
